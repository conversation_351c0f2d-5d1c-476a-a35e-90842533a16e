from enum import Enum

class AlertType(Enum):
    """
    Enumeration for different types of alerts with their corresponding severity levels.
    
    This enum defines the available alert types that can be used to categorize
    notifications sent to Microsoft Teams. Each type has an associated color scheme
    and icon for visual distinction.
    
    Attributes:
        SUCCESS: Indicates successful operations or positive status updates
        WARNING: Indicates potential issues that require attention but are not critical
        ERROR: Indicates failures or problems that need immediate attention
        INFO: Indicates general informational messages or status updates
        CRITICAL: Indicates severe issues that require urgent intervention
    """
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    INFO = "info"
    CRITICAL = "critical"