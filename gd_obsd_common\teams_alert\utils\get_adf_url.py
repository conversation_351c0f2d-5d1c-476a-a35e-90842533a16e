import os

# This avoids recreating the dictionary on every function call
SUBSCRIPTIONS = {
    "dev": "f7e64f22-c6a3-4a34-b0a3-d7ab35a8e8f8",
    "uat": "f7e64f22-c6a3-4a34-b0a3-d7ab35a8e8f8",
    "prod": "f7e64f22-c6a3-4a34-b0a3-d7ab35a8e8f8"
}
DEFAULT_ENV = "dev"
ENVIRONMENT_VAR = "ENVIRONMENT"

# Base URL for Azure DataFactory monitoring
ADF_MONITORING_BASE_URL = "https://adf.azure.com/en/monitoring/pipelineruns"
RESOURCE_GROUP_TEMPLATE = "rg-obsd-adf-{env}-dge-aen-001"
FACTORY_NAME_TEMPLATE = "adf-obsd-{env}-dge-aen-001"

def get_adf_url(run_id: str) -> str:
    """
    Generates a monitoring URL for a specific Azure Data Factory pipeline run.

    Args:
        run_id: The ID of the pipeline run.

    Returns:
        The full URL to the pipeline run monitoring page.

    Raises:
        ValueError: If the environment specified in the 'ENVIRONMENT'
                    environment variable is not a valid one.
    """
    # Best Practice & Error Handling: Centralize environment determination.
    env = os.environ.get(ENVIRONMENT_VAR, DEFAULT_ENV)

    # Error Handling: Validate the environment to prevent KeyErrors.
    subscription_id = SUBSCRIPTIONS.get(env)
    if not subscription_id:
        raise ValueError(
            f"Invalid environment '{env}'. "
            f"Supported environments are: {list(SUBSCRIPTIONS.keys())}"
        )

    
    resource_group = RESOURCE_GROUP_TEMPLATE.format(env=env)
    factory_name = FACTORY_NAME_TEMPLATE.format(env=env)

    # Construct the URL
    factory_path = (
        f"/subscriptions/{subscription_id}"
        f"/resourceGroups/{resource_group}"
        f"/providers/Microsoft.DataFactory/factories/{factory_name}"
    )
    return f"{ADF_MONITORING_BASE_URL}/{run_id}?factory={factory_path}"