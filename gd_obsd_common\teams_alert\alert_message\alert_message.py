from gd_obsd_common.teams_alert.alert_type.alert_type import AlertType

class AlertMessage:
    """
    Represents an alert message with title, content, and type classification.
    
    This class encapsulates the basic information needed for an alert notification,
    including the alert title, message content, and severity type. It serves as a
    data container for alert information that will be formatted and sent to Teams.
    
    Attributes:
        title (str): The alert title/subject line
        message (str): The detailed alert message content
        alert_type (AlertType): The type/severity level of the alert
    """
    
    def __init__(self, title: str, message: str, alert_type: AlertType = AlertType.INFO) -> None:
        """
        Initialize an AlertMessage instance.
        
        Args:
            title (str): The title or subject of the alert. This will be prominently
                        displayed in the Teams notification.
            message (str): The main content/body of the alert message. This should
                          contain the detailed information about the alert.
            alert_type (AlertType, optional): The type of alert which determines the
                                            visual styling (color, icon). Defaults to
                                            AlertType.INFO if not specified.
        
        Returns:
            None
        """
        self.title = title
        self.message = message
        self.alert_type = alert_type

    def __repr__(self) -> str:
        return f"AlertMessage(title={self.title!r}, message={self.message!r}, alert_type={self.alert_type!r})"
