from gd_obsd_common.teams_alert.alert_message.alert_message import AlertMessage
from gd_obsd_common.teams_alert.alert_type.alert_type import AlertType
import requests
import json
from typing import Dict, List, Optional

class TeamsAlert:
    """
    A Microsoft Teams webhook client for sending formatted alert notifications.
    
    This class provides functionality to send various types of alerts to Microsoft Teams
    channels using incoming webhooks. It supports different alert types with appropriate
    color coding, icons, and can include additional metrics and actionable buttons.
    
    The class handles the formatting of messages according to the Microsoft Teams
    MessageCard schema and provides methods to send alerts with optional metrics
    and action buttons.
    
    Attributes:
        teams_webhook_url (str): The Microsoft Teams webhook URL for sending messages
        color_schemes (Dict[AlertType, str]): Mapping of alert types to hex color codes
        icons (Dict[AlertType, str]): Mapping of alert types to emoji icons
    """
    
    def __init__(self, teams_webhook_url: str) -> None:
        """
        Initialize the TeamsAlert client with a webhook URL.
        
        Sets up the Teams webhook client with the provided URL and initializes
        the color schemes and icons for different alert types. The color schemes
        and icons are used to provide visual distinction between different types
        of alerts in the Teams interface.
        
        Args:
            teams_webhook_url (str): The Microsoft Teams incoming webhook URL.
                                   This URL is obtained when creating an incoming
                                   webhook connector in a Teams channel.
        
        Returns:
            None
        """
        self.teams_webhook_url = teams_webhook_url
        
        # Color schemes for different alert types (hex color codes without #)
        self.color_schemes = {
            AlertType.SUCCESS: "00FF00",  # Green
            AlertType.WARNING: "FFA500",  # Orange
            AlertType.ERROR: "FF0000",    # Red
            AlertType.INFO: "0076D7",     # Blue
            AlertType.CRITICAL: "8B0000"  # Dark Red
        }
        
        # Icons for different alert types (emoji symbols)
        self.icons = {
            AlertType.SUCCESS: "✅",
            AlertType.WARNING: "⚠️",
            AlertType.ERROR: "❌",
            AlertType.INFO: "ℹ️",
            AlertType.CRITICAL: "🚨"
        }
    
    def _send_message(self, message: Dict) -> bool:
        """
        Internal method to send a formatted message to the Teams webhook.
        
        This private method handles the actual HTTP POST request to the Teams webhook
        URL. It sends the message as JSON and handles the response, providing
        feedback on success or failure.
        
        Args:
            message (Dict): A dictionary containing the formatted message according
                          to the Microsoft Teams MessageCard schema. This should
                          include required fields like @type, @context, and content.
        
        Returns:
            bool: True if the message was successfully sent (HTTP 200), False otherwise.
                 Success/failure information is also printed to the console.
        
        Raises:
            requests.exceptions.RequestException: If there are network-related issues
                                                with the HTTP request.
        """
        response = requests.post(
            self.teams_webhook_url, 
            data=json.dumps(message), 
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ Message successfully sent.")
            return True
        else:
            print(f"❌ Error sending the message. Code: {response.status_code}, Answer: {response.text}")
            return False
    
    def send_alert(self, alert_message: AlertMessage, metrics: Optional[Dict[str, str]] = None, actions: Optional[List[Dict[str, str]]] = None) -> bool:
        """
        Send a comprehensive alert to Microsoft Teams with optional metrics and actions.
        
        This method creates and sends a formatted alert message to Teams. The alert
        includes the basic message information with appropriate color coding and icons
        based on the alert type. Optionally, it can include structured metrics data
        and actionable buttons for quick access to related resources.
        
        Args:
            alert_message (AlertMessage): The alert message object containing the title,
                                        message content, and alert type. This provides
                                        the core information for the notification.
            
            metrics (Optional[Dict[str, str]], optional): A dictionary of key-value pairs
                                                        representing metrics or additional
                                                        structured data to display. Keys
                                                        become field names and values become
                                                        field values in the Teams message.
                                                        Defaults to None.
            
            actions (Optional[List[Dict[str, str]]], optional): A list of dictionaries
                                                              representing actionable buttons.
                                                              Each dictionary should contain
                                                              'name' and 'url' keys for the
                                                              button label and target URL.
                                                              Defaults to None.
        
        Returns:
            bool: True if the alert was successfully sent to Teams, False if there
                 was an error during transmission. Detailed error information is
                 printed to the console in case of failure.
        
        Example:
            >>> teams_alert = TeamsAlert("https://outlook.office.com/webhook/...")
            >>> alert = AlertMessage("Server Down", "Web server is not responding", AlertType.ERROR)
            >>> metrics = {"Server": "web-01", "Status": "Offline", "Last Seen": "10:30 AM"}
            >>> actions = [{"name": "Check Logs", "url": "https://logs.example.com"}]
            >>> success = teams_alert.send_alert(alert, metrics=metrics, actions=actions)
        """
        body = [
            {
                "type": "TextBlock",
                "text": f"{self.icons[alert_message.alert_type]} **{alert_message.title}**",
                "wrap": True,
                "size": "Large",
                "weight": "Bolder"
            },
            {
                "type": "TextBlock",
                "text": alert_message.message,
                "wrap": True,
                "spacing": "Medium"
            }
        ]

        if metrics:
            factset = {
                "type": "FactSet",
                "facts": [{"title": k, "value": str(v)} for k, v in metrics.items()]
            }
            body.append(factset)

        actions_list = []
        if actions:
            actions_list.extend(
                {
                    "type": "Action.OpenUrl",
                    "title": action["name"],
                    "url": action["url"],
                }
                for action in actions
            )
        # Build Adaptive Card payload
        adaptive_card_payload = {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "contentUrl": None,
                    "content": {
                        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                        "type": "AdaptiveCard",
                        "version": "1.5",
                        "body": body,
                        "actions": actions_list
                    }
                }
            ]
        }

        print(f"Sending alert {alert_message} to Teams...")
        return self._send_message(adaptive_card_payload)