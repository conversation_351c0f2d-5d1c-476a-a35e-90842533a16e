repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.6.0  # Get the latest from: https://github.com/pre-commit/pre-commit-hooks
  hooks:
    - id: trailing-whitespace
      args: [--markdown-linebreak-ext=md]
    - id: end-of-file-fixer
    - id: check-added-large-files
    - id: check-yaml
    - id: requirements-txt-fixer

- repo: https://github.com/pycqa/isort
  rev: 5.13.2  # Get the latest from: https://github.com/pycqa/isort
  hooks:
    - id: isort

- repo: https://github.com/pycqa/flake8
  rev: 7.1.0  # Get the latest from: https://github.com/pycqa/flake8
  hooks:
  - id: flake8

- repo: https://github.com/PyCQA/bandit
  rev: 1.7.9  # Get the latest from: https://github.com/PyCQA/bandit
  hooks:
  - id: bandit
    args: ["-c", ".bandit.yml"]