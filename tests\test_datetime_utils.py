import pytest
from datetime import datetime, timedelta, timezone
import pytz
from gd_obsd_common.datetime_utils.datetime_utils import DateTimeUtils

class TestDateTimeUtils:
    """Test suite for DateTimeUtils class."""
    
    def test_str_to_datetime(self):
        """Test string to datetime conversion."""
        dt_utils = DateTimeUtils()
        date_str = '2025-05-07 15:12:00'
        expected = datetime(2025, 5, 7, 15, 12, 0)
        
        result = dt_utils.str_to_datetime(date_str)
        
        assert result == expected
        assert isinstance(result, datetime)
    
    def test_datetime_to_str(self):
        """Test datetime to string conversion."""
        dt_utils = DateTimeUtils()
        date_obj = datetime(2025, 5, 7, 15, 12, 0)
        expected = '2025-05-07 15:12:00'
        
        result = dt_utils.datetime_to_str(date_obj)
        
        assert result == expected
        assert isinstance(result, str)