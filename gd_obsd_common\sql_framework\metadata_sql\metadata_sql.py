# Databricks notebook source
# MAGIC %md
# MAGIC # MetadataSQL
# MAGIC Class to interact with the SQL DB that contains all the framework metadata.

# COMMAND ----------

from pyspark.sql import DataFrame, SparkSession
import os


class MetadataSQL:
    """
        Initializes the MetadataSQL class with Spark session and environment-specific SQL DB connection details.
        Sets up the project, environment, and constructs the JDBC URL for database interactions.
        
        Args:
            spark (SparkSession): The Spark session to use for database operations.
        """

    def __init__(self, spark: SparkSession) -> None:
        
        self.spark = spark

        # Set up project and environment
        project = "obsd"
        environment = os.environ.get('ENVIRONMENT', "dev")
        
        # SQL DB connection details
        sql_db_hostname = f"svr-sql-{project}-{environment}-dge-aen-001.database.windows.net"
        sql_db_port = 1433
        sql_db_name = f"sqldb-{project}-{environment}-dge-aen-001"
        self.jdbc_url = f"jdbc:sqlserver://{sql_db_hostname}:{sql_db_port};database={sql_db_name};Authentication=ActiveDirectoryMSI;"
        
    def read(self, query: str) -> DataFrame:
        """
        Execute a query in the SQL DB and return the results as a DataFrame.
        Args:
            query (str): query to be executed.
        Returns:
            DataFrame: query output as DataFrame.
        """
        return self.spark.read.jdbc(url=self.jdbc_url, table=f"({query}) AS tmp")
    
    def read_table(self, table: str) -> DataFrame:
        """
        Retrieve the content of a SQL DB table a DataFrame.
        Args:
            table (str):  name of the table in SQL Server to be retrieved.
        Returns:
            DataFrame: table contaent as DataFrame.
        """
        return self.spark.read.jdbc(url=self.jdbc_url, table=table)
    
    def write(self, df: DataFrame, table: str, mode: str = "append") -> None:
        """
        Writes a DataFrame in a SQL DB table.
        Args:
            df (DataFrame): dataframe containing the data to be stored.
            table (str): name of the table in SQL Server.
            mode (str): append, overwrite, etc.
        """
        df.write.jdbc(url=self.jdbc_url, table=table, mode=mode)

