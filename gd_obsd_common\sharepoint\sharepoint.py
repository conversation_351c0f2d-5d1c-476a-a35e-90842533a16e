import logging
import requests
import time
import pandas as pd
import json
from datetime import datetime, timedelta
from requests.exceptions import RequestException, ConnectionError, Timeout
from azure.core.exceptions import ClientAuthenticationError
import socket
from azure.identity import ClientSecretCredential
from msgraph import GraphServiceClient
from pyspark.sql import SparkSession, Row, DataFrame
from pyspark.sql.functions import col, explode
from pyspark.sql.types import StructType, ArrayType
 
class SharePoint:
    
    # Class to interact with SharePoint.
    
    def __init__(self) -> None:
        self.hostname = "adgov.sharepoint.com"
        # Initialize Microsoft Graph client
        self.credentials = ClientSecretCredential(
            tenant_id="0cefd05a-5b38-4ce7-96bb-c31e6e251d18",
            client_id=dbutils.secrets.get(scope="kv-app", key="common-sharepoint-client-id"),
            client_secret=dbutils.secrets.get(scope="kv-app", key="common-sharepoint-client-secret")
        )
        scopes = ['https://graph.microsoft.com/.default']
        self.graph_client = GraphServiceClient(self.credentials, scopes)
        # Acquire token right after initialization
        self._acquire_access_token()

    def _acquire_access_token(self, max_attempts=5, base_delay=2.0):
        attempt = 0
        while attempt < max_attempts:
            attempt += 1
            try:
                scopes = ['https://graph.microsoft.com/.default']
                logging.info("Generate SharePoint access token")
                self.access_token = self.credentials.get_token(scopes[0]).token
                logging.info("New access token acquired!")
                return
            except (ClientAuthenticationError, ConnectionResetError, socket.error) as e:
                logging.warning(f"Auth error: {repr(e)}")
                if attempt == max_attempts:
                    raise
                sleep_time = base_delay * (2 ** (attempt - 1))
                logging.info(f"Retrying after {sleep_time:.1f} seconds...")
                time.sleep(sleep_time)

    def _make_msgraph_call(self, request_url: str, max_attempts: int = 5, base_delay: float = 2.0) -> requests.Response:
  
        # Makes Microsoft Graph API call with retry and token renewal.
        
        # :param request_url: Full MS Graph API URL
        # :param max_attempts: Max number of attempts
        # :param base_delay: Initial delay for exponential backoff in seconds
        # :return: requests.Response object

        attempt_count = 0

        while attempt_count < max_attempts:
            attempt_count += 1
            try:
                logging.info(f"MS Graph call to URL: {request_url}. Attempt {attempt_count}/{max_attempts}")
                response = requests.get(
                    request_url,
                    headers={
                        "Authorization": f"Bearer {self.access_token}",
                        "Accept": "application/json",
                        "Content-Type": "application/json"
                    },
                    timeout=30  # optional: prevent hanging
                )

                status_code = response.status_code

                # Success
                if status_code == 200:
                    return response

                # Token expired
                elif status_code == 401:
                    logging.warning("Access token expired. Renewing token.")
                    self._acquire_access_token()

                # Redirect or temporary unavailability
                elif status_code in (302, 503):
                    logging.warning(f"Temporary issue ({status_code}). Retrying...")

                # Forbidden: no point retrying
                elif status_code == 403:
                    raise Exception(
                        f"Access forbidden. Check app permissions. {status_code}: {response.text}"
                    )

                # Not found: handled gracefully
                elif status_code == 404:
                    logging.warning(f"Resource not found: {request_url}")
                    return response

                # Other non-200 responses
                else:
                    logging.warning(f"Received unexpected status code {status_code}: {response.text}")

            except (ConnectionError, Timeout, RequestException) as e:
                logging.warning(f"Request error: {e}. Attempt {attempt_count}/{max_attempts}")

            # Wait before retrying
            sleep_time = base_delay * (2 ** (attempt_count - 1))
            logging.info(f"Waiting {sleep_time:.1f} seconds before retrying...")
            time.sleep(sleep_time)

        # Final failure
        raise Exception(f"MS Graph call to {request_url} failed after {max_attempts} attempts.")   
 
    def _get_site_id(self, url: str) -> str:
        
        # Retrieve site id from the given SharePoint url.
        # :param url: Url to extract the site from.
        # :return: site id
      
        # Extract site name from url
        if '/teams/' in url:
            site_name = url.split('/teams/')[1].split('/')[0]
            endpoint = f":/teams/{site_name}"  # Teams sites require :/teams/ in endpoint
        elif '/sites/' in url:
            site_name = url.split('/sites/')[1].split('/')[0]
            endpoint = f":/sites/{site_name}"  # Site sites require :/teams/ in endpoint
        else:
            endpoint = ''  # Default site
        url = f"https://graph.microsoft.com/v1.0/sites/{self.hostname}{endpoint}"
        response = self._make_msgraph_call(url)
        site_id = response.json()["id"]
        logging.info(f"Retrieved site id '{site_id}' from host '{self.hostname}' and site '{site_name}'")

        return site_id
 
    def get_file_metadata(self, url: str) -> dict:
	        """
	        Returns the metadata of a SharePoint file as a JSON dictionary.
	        :param url: Full SharePoint file URL.
	        :return: File metadata (name, size, timestamps, etc.) as a dict.
	        :raises FileNotFoundError: If the file does not exist.
	        """
	        # Extract site ID using your existing method
	        site_id = self._get_site_id(url)
	
	        # Extract the relative file path after 'Shared Documents'
	        relative_path = url.split("/Shared%20Documents/")[1].split('?')[0]
	
	        # Build the request URL (without '/content' to get metadata instead of file bytes)
	        request_url = (
	            f"https://graph.microsoft.com/v1.0/"
	            f"sites/{site_id}/drive/root:/{relative_path}"
	        )
	
	        # Make the Microsoft Graph API call
	        response = self._make_msgraph_call(request_url)
	
	        if response.status_code == 404:
	            # File not found
	            raise FileNotFoundError(
	                f"File not found at the provided URL {url}. "
	                f"Please check the path and permissions. Details: {response.text}"
	            )
	        response.raise_for_status()
	
	        # Return the file metadata as a dictionary
	        return response.json()
    def read_file_from_url(self, url: str) -> bytes:
        
        # Read SharePoint file content as bytes from the given url.
        # :param url: SharePoint file's url.
        # :return: content as bytes.
 
        site_id = self._get_site_id(url)
 
        relative_path = url.split(f"/Shared%20Documents/")[1].split('?')[0]
        request_url = f'https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root:/{relative_path}:/content'
 
        response = self._make_msgraph_call(request_url)
        if response.status_code == 404:
            # If not found return None
            raise FileNotFoundError(f"File not found in given url {url}. Please review the url and make sure the file is present in the corresponding path. Full error: {response.text}")
        # Return file content as bytes
        return response.content
    
    def get_lists(self, url: str, output_type: str="") -> list:
        
        # Get all available lists from Sharepoint
        
        site_id = self._get_site_id(url)
        
        lists_response = requests.get(f"https://graph.microsoft.com/v1.0/sites/{site_id}/lists",
                                      headers={
                                          "Authorization": f"Bearer {self.access_token}",
                                          "Accept": "application/json",
                                          "Content-Type": "application/json"
                                      })
        lists = lists_response.json()
        
        if output_type == "table":
            lists = SharePoint.flatten_df(lists)
        elif output_type == "json":
            lists = json.dumps(lists)
        else:
            pass
            
        return lists
    
    def get_list_content(self, url: str, list_id: str, output_type: str=""):
        
        # Get list content.

        def clean_value(value):
            """
            Clean values to make them compatible with PySpark.
            """
            import datetime

            if isinstance(value, (int, float, bool, str, type(None))):
                return value
            elif isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, list):
                return str(value)
            elif isinstance(value, dict):
                return str(value)
            else:
                return str(value)

        site_id = self._get_site_id(url)

        items = [] 
        list_id = list_id
        url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/lists/{list_id}/items?$expand=fields"
        while url:
            items_response = requests.get(
                url,
                headers={
                    "Authorization": f"Bearer {self.access_token}",
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                })

            item = items_response.json()
            items.extend(item.get('value', []))
            url = item.get('@odata.nextLink', None)

        data = [item['fields'] for item in items]

        if output_type == "table":
            output = SharePoint.flatten_df(data)
        elif output_type == "json":
            # output = json.dumps(items)
            output = items
        elif output_type == "response":
            output = items_response
        else:
            pass

        return output
    
    # Function to Transform JSON into Table 
    @staticmethod
    def flatten_df(lists: dict) -> DataFrame:
        
        # Recursively flattens a PySpark DataFrame with structs and arrays,
        # handling column names with special characters.
        
        def safe_col(col_name: str):
            
            # Returns a safe `col` expression for column names with special characters.
            
            if any(c in col_name for c in ['.', '@', ' '] ):
                return col(f"`{col_name}`")
            else:
                return col(col_name)

        complex_fields = True

        pdf = pd.json_normalize(lists)
        df = spark.createDataFrame(pdf)

        while complex_fields:
            complex_fields = False
            new_cols = []
            for field in df.schema.fields:
                field_name = field.name
                field_type = field.dataType

                if isinstance(field_type, StructType):
                    # Decompose struct
                    for subfield in field_type.fields:
                        sub_name = subfield.name
                        new_name = f"{field_name}_{sub_name}"
                        new_cols.append(col(f"`{field_name}`.`{sub_name}`").alias(new_name))
                    complex_fields = True

                elif isinstance(field_type, ArrayType) and isinstance(field_type.elementType, StructType):
                    # Explode array of structs
                    df = df.withColumn(field_name, explode(safe_col(field_name)))
                    complex_fields = True
                    new_cols.append(safe_col(field_name))

                else:
                    new_cols.append(safe_col(field_name))

            df = df.select(*new_cols)

        return df   
    
    def get_list_content_with_lookups(self, url: str, list_id: str, output_type: str = "", utc_offset: int = 0):
        # Get list content from SharePoint via Graph API.

        def clean_value(value):
            """
            Clean values to make them compatible with PySpark.
            """
            import datetime

            if isinstance(value, (int, float, bool, str, type(None))):
                return value
            elif isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, list):
                return str(value)
            elif isinstance(value, dict):
                return str(value)
            else:
                return str(value)

        site_id = self._get_site_id(url)

        items = []
        url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/lists/{list_id}/items?$expand=fields"

        while url:
            items_response = requests.get(
                url,
                headers={
                    "Authorization": f"Bearer {self.access_token}",
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )

            item = items_response.json()
            items.extend(item.get('value', []))
            url = item.get('@odata.nextLink', None)

        def find_unique_keys_with_substring(json_obj, substring, prefix=''):
            matches = []
            if isinstance(json_obj, dict):
                for key, value in json_obj.items():
                    full_key = f"{prefix}.{key}" if prefix else key
                    if substring in key:
                        matches.append(full_key)
                    matches.extend(find_unique_keys_with_substring(value, substring, full_key))
            elif isinstance(json_obj, list):
                for item in json_obj:
                    matches.extend(find_unique_keys_with_substring(item, substring, prefix))

            return list(dict.fromkeys(matches))

        def build_graph_api_url(site_id, list_id, lookup_fields):
            base_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/lists/{list_id}/items"

            # Build combined list: LookupId + base name without LookupId
            select_fields = []
            for field in lookup_fields:
                if field.startswith("fields.") and field.endswith("LookupId"):
                    lookup_id_field = field.replace("fields.", "")
                    base_name = lookup_id_field.replace("LookupId", "")
                    select_fields.extend([lookup_id_field, base_name])

            select_param = ",".join(select_fields)
            full_url = f"{base_url}?$expand=fields($select=fields,{select_param})"
            return full_url

        # Identify all fields that are LookupId references
        lookup_fields = find_unique_keys_with_substring(items, "LookupId")

        if len(lookup_fields) > 0:
            url = build_graph_api_url(site_id, list_id, lookup_fields)
            items_lookup = []

            while url:
                items_response = requests.get(
                    url,
                    headers={
                        "Authorization": f"Bearer {self.access_token}",
                        "Accept": "application/json",
                        "Content-Type": "application/json"
                    }
                )

                item = items_response.json()
                items_lookup.extend(item.get('value', []))
                url = item.get('@odata.nextLink', None)

            def merge_lookup_fields(original_data, expanded_data, prefix="lkp_"):
                original_map = {
                    item["id"]: item.get("fields", {})
                    for item in original_data
                    if isinstance(item, dict) and "id" in item
                }
                expanded_map = {
                    item["id"]: item.get("fields", {})
                    for item in expanded_data
                    if isinstance(item, dict) and "id" in item
                }

                merged_items = []
                for item_id, fields_original in original_map.items():
                    fields_expanded = expanded_map.get(item_id, {})

                    # Prefix lookup fields to avoid name collisions
                    renamed_expanded = {f"{prefix}{k}": v for k, v in fields_expanded.items()}

                    # Combine both dictionaries
                    merged_fields = {**fields_original, **renamed_expanded}
                    merged_items.append({"id": item_id, "fields": merged_fields})

                return merged_items

            # Merge original and expanded lookup fields
            items = merge_lookup_fields(items, items_lookup)

        def convert_datetimes_utc_offset(obj, offset_hours=utc_offset):
            """
            Recursively convert all datetime fields to UTC+offset_hours
            in a nested dict or list structure.
            """
            if isinstance(obj, dict):
                return {
                    k: convert_datetimes_utc_offset(v, offset_hours)
                    for k, v in obj.items()
                }
            elif isinstance(obj, list):
                return [convert_datetimes_utc_offset(v, offset_hours) for v in obj]
            elif isinstance(obj, str):
                try:
                    # Try to parse string as ISO datetime
                    dt = datetime.fromisoformat(obj.replace("Z", "+00:00"))
                    dt_offset = dt + timedelta(hours=offset_hours)
                    return dt_offset.isoformat()
                except ValueError:
                    return obj  # Not a datetime string
            else:
                return obj
        
        items = [convert_datetimes_utc_offset(record, utc_offset) for record in items]

        # Extract only the "fields" dictionary from each item
        data = [item['fields'] for item in items]

        # Return based on output_type
        if output_type == "table":
            output = SharePoint.flatten_df(data)
        elif output_type == "json":
            output = items
        elif output_type == "response":
            output = items_response
        else:
            output = None

        return output